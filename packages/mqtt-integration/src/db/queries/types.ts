// Base types for database entities

// Utility type for type manipulation
export type OmitAndMerge<T, O<PERSON><PERSON><PERSON><PERSON> extends keyof T, Merge> = Omit<
  Omit<T, OmitKeys>,
  keyof Merge
> &
  Merge;

export const DEVICE_MODEL_VALUES = [
  "LIC",
  "WPC-PL10",
  "WPC-PL50",
  "VC",
  "RM",
] as const;

export type DeviceModel = (typeof DEVICE_MODEL_VALUES)[number];

export type Project = {
  id: string;
  property: string;
  name: string;
  irrigation_water_pump: string;
  fertigation_water_pump: string | null;
  backwash_pump_type: "IRRIGATION" | "FERTIGATION" | null;
  localized_irrigation_controller: string;
  description: string | null;
  start_date: Date | string | null;
  end_date: Date | string | null;
  date_created: Date | string;
  user_created: string | null;
  date_updated: Date | string;
  user_updated: string | null;
  metadata: any;
  notes: string | null;
};

export type Device = {
  id: string;
  identifier: string;
  model: DeviceModel;
  date_created: Date | string;
  user_created: string | null;
  date_updated: Date | string;
  user_updated: string | null;
  metadata: any;
  notes: string | null;
};

export type WaterPump = {
  id: string;
  property: string;
  water_pump_controller: string | null;
  label: string;
  identifier: string;
  pump_type: "IRRIGATION" | "FERTIGATION" | "SERVICE";
  pump_model: string | null;
  has_frequency_inverter: boolean;
  monitor_operation: boolean;
  flow_rate_lh: number | null;
  mode: "PULSE" | "CONTINUOUS";
  notes: string | null;
  metadata: any;
  date_created: Date | string;
  user_created: string | null;
  date_updated: Date | string;
  user_updated: string | null;
};

export type Sector = {
  id: string;
  project: string;
  name: string;
  valve_controller: string;
  valve_controller_output: number;
  description: string | null;
  power: number;
  date_created: Date | string;
  user_created: string | null;
  date_updated: Date | string;
  user_updated: string | null;
  metadata: any;
  notes: string | null;
};

export type IrrigationPlan = {
  id: string;
  project: string;
  name: string;
  description: string | null;
  start_time: string; // time without time zone
  days_of_week: string[]; // jsonb array
  is_enabled: boolean;
  fertigation_enabled: boolean;
  backwash_enabled: boolean;
  total_irrigation_duration: number;
  start_date: Date | string | null; // date
  end_date: Date | string | null; // date
  date_created: Date | string;
  user_created: string | null;
  date_updated: Date | string;
  user_updated: string | null;
  metadata: any;
  notes: string | null;
};

export type IrrigationPlanStep = {
  id: string;
  irrigation_plan: string;
  sector: string;
  description: string | null;
  order: number;
  duration_seconds: number;
  fertigation_start_delay_seconds: number | null;
  fertigation_duration_seconds: number | null;
  date_created: Date | string;
  user_created: string | null;
  date_updated: Date | string;
  user_updated: string | null;
  metadata: any;
  notes: string | null;
};

// Composite types for queries with joined data

export type WaterPumpWithController = OmitAndMerge<
  WaterPump,
  "water_pump_controller",
  { water_pump_controller: Device }
>;

export type SectorWithValveController = Sector & {
  valve_controller_device: Device;
};

export type IrrigationPlanWithSteps = IrrigationPlan & {
  steps: IrrigationPlanStep[];
};

// Types for scheduling data with enhanced information
export type IrrigationPlanStepWithSector = IrrigationPlanStep & {
  sector_info: SectorWithValveController;
};

export type IrrigationPlanWithStepsAndSectors = IrrigationPlan & {
  steps: IrrigationPlanStepWithSector[];
};

export type ProjectWithSchedulingData = ProjectFull & {
  irrigation_plans: IrrigationPlanWithSteps[];
};

// Full project type with all related data (excluding irrigation plans)
export type ProjectFull = OmitAndMerge<
  Project,
  | "irrigation_water_pump"
  | "fertigation_water_pump"
  | "localized_irrigation_controller",
  {
    irrigation_water_pump: WaterPumpWithController;
    fertigation_water_pump: WaterPumpWithController | null;
    localized_irrigation_controller: Device;
    sectors: SectorWithValveController[];
  }
>;

/*
    id uuid NOT NULL DEFAULT gen_random_uuid(),
    property uuid NOT NULL,
    name character varying(255) NOT NULL,
    reservoir_monitor uuid,
    water_pump uuid,
    description text,
    capacity numeric(15,3),
    location geometry(Point,4326),
    notes text,
    date_created timestamp with time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    user_created uuid,
    date_updated timestamp with time zone,
    user_updated uuid,
    metadata jsonb,
    enabled boolean DEFAULT true NOT NULL,
    */

export type ReservoirWithMonitorAndWaterPumpWithController = {
  id: string;
  property: string;
  name: string;
  reservoir_monitor: Device | null;
  water_pump: WaterPumpWithController | null;
  description: string | null;
  capacity: number | null;
  safety_time_minutes: number | null;
  location: { type: "Point"; coordinates: [number, number] } | null;
  notes: string | null;
  date_created: Date | string;
  user_created: string | null;
  date_updated: Date | string;
  user_updated: string | null;
  metadata: any;
  enabled: boolean;
};

export type PropertyDevice = {
  id: string;
  property: string;
  device: string;
  current_mesh_device_mapping: string | null;
  start_date: Date | string | null;
  end_date: Date | string | null;
  notes: string | null;
  date_created: Date | string;
  user_created: string | null;
  date_updated: Date | string;
  user_updated: string | null;
  metadata: any;
};

export type Property = {
  id: string;
  account: string;
  name: string;
  backwash_duration_minutes: number | null;
  backwash_period_minutes: number | null;
  backwash_delay_seconds: number | null;
  rain_gauge_enabled: boolean;
  rain_gauge_resolution_mm: number | null;
  precipitation_volume_limit_mm: number | null;
  precipitation_suspended_duration_hours: number | null;
  timezone: string;
  point: { type: "Point"; coordinates: [number, number] } | null;
  address_postal_code: string | null;
  address_street_name: string | null;
  address_street_number: string | null;
  address_complement: string | null;
  address_neighborhood: string | null;
  address_city: string | null;
  address_state: string | null;
  address_country: string | null;
  notes: string | null;
  date_created: Date | string;
  user_created: string | null;
  date_updated: Date | string;
  user_updated: string | null;
  metadata: any;
};

export type PropertyDeviceWithDevice = OmitAndMerge<
  PropertyDevice,
  "device",
  { device: Device }
>;

export type PropertyDeviceWithDeviceAndProperty = OmitAndMerge<
  PropertyDeviceWithDevice,
  "property",
  { property: Property }
>;

export type CurrentLICPacket = {
  id: number;
  date_created: Date | string;
  device: string;
  packet_date: Date | string;
  payload_type: string;
  payload_data: any;
};

export type CurrentProjectState = {
  id: string;
  project: string;
  date_created: Date | string;
  date_updated: Date | string;
  packet_date: Date | string;
  irrigation_status: "active" | "inactive" | "error";
  fertigation_status: "active" | "inactive" | "error";
  backwash_status: "active" | "inactive" | "error";
  sectors: Array<{
    sector: string;
    sector_name: string;
    status: "active" | "inactive" | "error";
  }>;
};

export type CurrentIrrigationPlanState = {
  id: string;
  irrigation_plan: string;
  packet_date: Date | string;
  start_time: Date | string;
  end_time: Date | string | null;
  activated_steps: string[];
  activated_ferti_steps: string[];
  waterpump_working: boolean;
  backwash_start_time: Date | string | null;
  uses_waterpump: boolean;
  uses_ferti: boolean;
  date_created: Date | string;
  date_updated: Date | string;
};

// Device Message Request types
export const DEVICE_MESSAGE_REQUEST_STATUS_VALUES = [
  "pending",
  "processing",
  "sent",
  "acknowledged",
  "failed",
  "expired",
  "cancelled",
] as const;

export type DeviceMessageRequestStatus =
  (typeof DEVICE_MESSAGE_REQUEST_STATUS_VALUES)[number];

export const DEVICE_MESSAGE_REQUEST_PAYLOAD_TYPE_VALUES = [
  "config",
  "devices",
  "scheduling",
  "dev_scheduling",
  "automation",
  "control",
  "command",
  "request_info",
  "firmware_update",
] as const;

export type DeviceMessageRequestPayloadType =
  (typeof DEVICE_MESSAGE_REQUEST_PAYLOAD_TYPE_VALUES)[number];

export type DeviceMessageRequest = {
  id: string;

  // Device Relationships
  device: string;
  property_device: string | null;

  // Message Content
  packet_id: number;
  payload_type: DeviceMessageRequestPayloadType;
  payload_data: Record<string, any>;
  payload_bytes: Buffer | null;
  message_hash: string | null;

  // Message Relationships & Correlation
  parent_message_id: string | null;
  correlation_id: string | null;

  // Delivery Control
  status: DeviceMessageRequestStatus;
  priority: number;

  // Scheduling & Timing
  scheduled_at: Date | string;
  expires_at: Date | string | null;
  sent_at: Date | string | null;
  acknowledged_at: Date | string | null;

  // Retry & Error Handling
  attempts: number;
  max_attempts: number;
  retry_delay_seconds: number | null;
  last_error: string | null;

  // Standard Audit Fields
  date_created: Date | string;
  user_created: string | null;
  date_updated: Date | string;
  user_updated: string | null;
  metadata: Record<string, any> | null;
  notes: string | null;
};

export type DeviceMessageRequestWithDevice = OmitAndMerge<
  DeviceMessageRequest,
  "device",
  { device: Device }
>;

// LIC State types
export type LICStateRecord = {
  device: string;

  // LICState JSONB columns
  lic: Record<string, any> | null;
  groups: Record<string, any>[] | null;
  devices: Record<string, any>[] | null;
  mesh_devices: Record<string, any>[] | null;
  schedules: Record<string, any>[] | null;
  sector_schedules: Record<string, any>[] | null;
  device_schedules: Record<string, any>[] | null;

  // Request timestamps
  last_devices_request: number | null;
  last_scheduling_request: number | null;
  last_dev_scheduling_request: number | null;
  last_automation_request: number | null;
  last_config_request: number | null;

  // Current timestamps from InfoPackage
  current_devices_timestamp: number | null;
  current_scheduling_timestamp: number | null;
  current_dev_scheduling_timestamp: number | null;
  current_automation_timestamp: number | null;
  current_config_timestamp: number | null;

  // Standard audit fields
  date_created: Date | string;
  user_created: string | null;
  date_updated: Date | string;
  user_updated: string | null;
};

export type LICStateRecordWithDevice = OmitAndMerge<
  LICStateRecord,
  "device",
  { device: Device }
>;
