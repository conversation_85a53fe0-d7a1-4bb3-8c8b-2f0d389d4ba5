import {
  afterEach,
  beforeAll,
  beforeEach,
  describe,
  expect,
  it,
} from "bun:test";
import { Knex } from "knex";
import {
  begin,
  createDatabase,
  createKnex,
  rollbackAndDestroy,
} from "./helpers/db";
import { insertDevice, insertLicState, insertUser } from "./helpers/fixtures";

let knex: Knex;
let trx: Knex.Transaction;

describe("LIC State History Table Population", () => {
  beforeAll(async () => {
    // Create test database programmatically
    await createDatabase("lic_state_history_test");
  });

  beforeEach(async () => {
    knex = createKnex({ database: "lic_state_history_test" });
    trx = await begin(knex);
  });

  afterEach(async () => {
    await rollbackAndDestroy(trx);
  });

  it("should verify the test database was created successfully", async () => {
    const result = await trx.raw("SELECT current_database() as db_name");
    expect(result.rows[0].db_name).toBe("lic_state_history_test");
  });

  async function setupLicDevice() {
    const userId = await insertUser(trx);
    const licDevice = await insertDevice(trx, "LIC", "TEST-LIC-001");

    return { userId, licDevice };
  }

  function closeMs(a: Date, b: Date, toleranceMs = 100): boolean {
    return Math.abs(a.getTime() - b.getTime()) <= toleranceMs;
  }

  it("should create a record in lic_state_history when inserting into lic_state", async () => {
    const { userId, licDevice } = await setupLicDevice();

    const testData = {
      lic: { codec: "test-codec", version: "1.0", config: { test: true } },
      groups: [{ id: 1, name: "Test Group" }],
      devices: [{ id: 1, type: "sensor" }],
      mesh_devices: [{ id: 1, address: "mesh001" }],
      schedules: [{ id: 1, name: "Test Schedule" }],
      sector_schedules: [{ id: 1, sector: 1 }],
      device_schedules: [{ id: 1, device: 1 }],
      last_devices_request: 1234567890,
      last_scheduling_request: 1234567891,
      last_dev_scheduling_request: 1234567892,
      last_automation_request: 1234567893,
      last_config_request: 1234567894,
      current_devices_timestamp: 1234567895,
      current_scheduling_timestamp: 1234567896,
      current_dev_scheduling_timestamp: 1234567897,
      current_automation_timestamp: 1234567898,
      current_config_timestamp: 1234567899,
      user_created: userId,
      user_updated: userId,
    };

    const beforeInsert = new Date();
    const licStateId = await insertLicState(trx, licDevice, testData);
    const afterInsert = new Date();

    // Verify the lic_state record was created
    const licStateRecord = await trx("lic_state")
      .where({ id: licStateId })
      .first();

    expect(licStateRecord).toBeDefined();
    expect(licStateRecord.device).toBe(licDevice);

    // Verify the lic_state_history record was created by the trigger
    const historyRecords = await trx("lic_state_history")
      .where({ device: licDevice })
      .orderBy("state_date", "desc");

    expect(historyRecords).toHaveLength(1);

    const historyRecord = historyRecords[0];
    expect(historyRecord.device).toBe(licDevice);

    // Verify state_date is set to date_created
    expect(
      closeMs(
        new Date(historyRecord.state_date),
        new Date(licStateRecord.date_created)
      )
    ).toBe(true);

    // Verify the state_date is within the expected time range
    expect(new Date(historyRecord.state_date).getTime()).toBeGreaterThanOrEqual(
      beforeInsert.getTime()
    );
    expect(new Date(historyRecord.state_date).getTime()).toBeLessThanOrEqual(
      afterInsert.getTime()
    );

    // Verify all data was copied correctly
    expect(historyRecord.lic).toEqual(testData.lic);
    expect(historyRecord.groups).toEqual(testData.groups);
    expect(historyRecord.devices).toEqual(testData.devices);
    expect(historyRecord.mesh_devices).toEqual(testData.mesh_devices);
    expect(historyRecord.schedules).toEqual(testData.schedules);
    expect(historyRecord.sector_schedules).toEqual(testData.sector_schedules);
    expect(historyRecord.device_schedules).toEqual(testData.device_schedules);
    expect(historyRecord.last_devices_request).toBe(
      testData.last_devices_request
    );
    expect(historyRecord.last_scheduling_request).toBe(
      testData.last_scheduling_request
    );
    expect(historyRecord.last_dev_scheduling_request).toBe(
      testData.last_dev_scheduling_request
    );
    expect(historyRecord.last_automation_request).toBe(
      testData.last_automation_request
    );
    expect(historyRecord.last_config_request).toBe(
      testData.last_config_request
    );
    expect(historyRecord.current_devices_timestamp).toBe(
      testData.current_devices_timestamp
    );
    expect(historyRecord.current_scheduling_timestamp).toBe(
      testData.current_scheduling_timestamp
    );
    expect(historyRecord.current_dev_scheduling_timestamp).toBe(
      testData.current_dev_scheduling_timestamp
    );
    expect(historyRecord.current_automation_timestamp).toBe(
      testData.current_automation_timestamp
    );
    expect(historyRecord.current_config_timestamp).toBe(
      testData.current_config_timestamp
    );
  });

  it("should create a new record in lic_state_history when updating lic_state", async () => {
    const { userId, licDevice } = await setupLicDevice();

    // First, insert a record
    const initialData = {
      lic: { codec: "initial-codec", version: "1.0" },
      groups: [{ id: 1, name: "Initial Group" }],
      user_created: userId,
      user_updated: userId,
    };

    const licStateId = await insertLicState(trx, licDevice, initialData);

    // Wait a small amount to ensure different timestamps
    await new Promise((resolve) => setTimeout(resolve, 10));

    // Update the record
    const updatedData = {
      lic: { codec: "updated-codec", version: "2.0" },
      groups: [
        { id: 1, name: "Updated Group" },
        { id: 2, name: "New Group" },
      ],
      devices: [{ id: 1, type: "updated-sensor" }],
      current_devices_timestamp: 9876543210,
    };

    const beforeUpdate = new Date();
    await trx("lic_state")
      .where({ id: licStateId })
      .update({
        ...updatedData,
        user_updated: userId,
      });
    const afterUpdate = new Date();

    // Verify the lic_state record was updated
    const licStateRecord = await trx("lic_state")
      .where({ id: licStateId })
      .first();

    expect(licStateRecord.lic).toEqual(updatedData.lic);
    expect(licStateRecord.groups).toEqual(updatedData.groups);

    // Verify we now have 2 history records (one from insert, one from update)
    const historyRecords = await trx("lic_state_history")
      .where({ device: licDevice })
      .orderBy("state_date", "asc");

    expect(historyRecords).toHaveLength(2);

    const [insertRecord, updateRecord] = historyRecords;

    // Verify the insert record has the initial data
    expect(insertRecord.lic).toEqual(initialData.lic);
    expect(insertRecord.groups).toEqual(initialData.groups);
    expect(
      closeMs(
        new Date(insertRecord.state_date),
        new Date(licStateRecord.date_created)
      )
    ).toBe(true);

    // Verify the update record has the updated data and state_date = date_updated
    expect(updateRecord.lic).toEqual(updatedData.lic);
    expect(updateRecord.groups).toEqual(updatedData.groups);
    expect(updateRecord.devices).toEqual(updatedData.devices);
    expect(updateRecord.current_devices_timestamp).toBe(
      updatedData.current_devices_timestamp
    );
    expect(
      closeMs(
        new Date(updateRecord.state_date),
        new Date(licStateRecord.date_updated)
      )
    ).toBe(true);

    // Verify the update state_date is within the expected time range
    expect(new Date(updateRecord.state_date).getTime()).toBeGreaterThanOrEqual(
      beforeUpdate.getTime()
    );
    expect(new Date(updateRecord.state_date).getTime()).toBeLessThanOrEqual(
      afterUpdate.getTime()
    );

    // Verify the update record is newer than the insert record
    expect(new Date(updateRecord.state_date).getTime()).toBeGreaterThan(
      new Date(insertRecord.state_date).getTime()
    );
  });

  it("should maintain data consistency between lic_state and lic_state_history after multiple operations", async () => {
    const { userId, licDevice } = await setupLicDevice();

    // Perform multiple INSERT and UPDATE operations
    const operations = [
      {
        type: "insert",
        data: {
          lic: { codec: "codec-v1", version: "1.0" },
          groups: [{ id: 1, name: "Group 1" }],
          user_created: userId,
          user_updated: userId,
        },
      },
      {
        type: "update",
        data: {
          lic: { codec: "codec-v2", version: "2.0" },
          groups: [{ id: 1, name: "Group 1 Updated" }],
          devices: [{ id: 1, type: "sensor" }],
        },
      },
      {
        type: "update",
        data: {
          lic: { codec: "codec-v3", version: "3.0" },
          groups: [
            { id: 1, name: "Group 1 Final" },
            { id: 2, name: "Group 2" },
          ],
          devices: [
            { id: 1, type: "sensor" },
            { id: 2, type: "actuator" },
          ],
          schedules: [{ id: 1, name: "Schedule 1" }],
        },
      },
    ];

    let licStateId: string;

    for (let i = 0; i < operations.length; i++) {
      const operation = operations[i]!;

      // Small delay to ensure different timestamps
      if (i > 0) {
        await new Promise((resolve) => setTimeout(resolve, 10));
      }

      if (operation.type === "insert") {
        licStateId = await insertLicState(trx, licDevice, operation.data);
      } else {
        await trx("lic_state")
          .where({ id: licStateId! })
          .update({
            ...operation.data,
            user_updated: userId,
          });
      }
    }

    // Verify the final state in lic_state table
    const finalLicState = await trx("lic_state")
      .where({ id: licStateId! })
      .first();

    const finalOperationData = operations[operations.length - 1]!.data;
    expect(finalLicState!.lic).toEqual(finalOperationData.lic);
    expect(finalLicState!.groups).toEqual(finalOperationData.groups);
    expect(finalLicState!.devices).toEqual(finalOperationData.devices);
    expect(finalLicState!.schedules).toEqual(finalOperationData.schedules);

    // Verify all operations are recorded in history
    const historyRecords = await trx("lic_state_history")
      .where({ device: licDevice })
      .orderBy("state_date", "asc");

    expect(historyRecords).toHaveLength(operations.length);

    // Verify each history record corresponds to the correct operation
    for (let i = 0; i < operations.length; i++) {
      const historyRecord = historyRecords[i]!;
      const operationData = operations[i]!.data;

      expect(historyRecord.lic).toEqual(operationData.lic);
      expect(historyRecord.groups).toEqual(operationData.groups);

      if (operationData.devices) {
        expect(historyRecord.devices).toEqual(operationData.devices);
      }

      if (operationData.schedules) {
        expect(historyRecord.schedules).toEqual(operationData.schedules);
      }
    }

    // Verify timestamps are in chronological order
    for (let i = 1; i < historyRecords.length; i++) {
      const prevDate = new Date(historyRecords[i - 1].state_date);
      const currDate = new Date(historyRecords[i].state_date);
      expect(currDate.getTime()).toBeGreaterThan(prevDate.getTime());
    }

    // Verify the most recent history record matches the current lic_state
    const mostRecentHistory = historyRecords[historyRecords.length - 1]!;
    expect(mostRecentHistory.lic).toEqual(finalLicState!.lic);
    expect(mostRecentHistory.groups).toEqual(finalLicState!.groups);
    expect(mostRecentHistory.devices).toEqual(finalLicState!.devices);
    expect(mostRecentHistory.schedules).toEqual(finalLicState!.schedules);
  });
});
