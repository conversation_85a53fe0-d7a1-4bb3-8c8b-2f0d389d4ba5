/**
 * Migration to create irrigation_plan_state table as TimescaleDB hypertable with monthly chunking and compression
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Create irrigation_plan_state table with same structure as current_irrigation_plan_state
    await tx.schema.createTable("irrigation_plan_state", (table) => {
      table.bigIncrements("id");
      table.uuid("irrigation_plan").notNullable();
      table.timestamp("packet_date").notNullable();
      table.timestamp("start_time").nullable();
      table.timestamp("end_time").nullable();
      table.jsonb("activated_steps").notNullable();
      table.jsonb("activated_ferti_steps").notNullable();
      table.boolean("waterpump_working").notNullable();
      table.timestamp("backwash_start_time").nullable();
      table.boolean("uses_waterpump").notNullable();
      table.boolean("uses_ferti").notNullable();
      table.timestamp("date_created").notNullable().defaultTo(knex.fn.now());

      // Foreign key constraint
      table.foreign("irrigation_plan").references("id").inTable("irrigation_plan");

      // Index for time-series queries (will be created before hypertable conversion)
      table.index(["packet_date"], "irrigation_plan_state_packet_date_idx");
      table.index(
        ["irrigation_plan", "packet_date"],
        "irrigation_plan_state_irrigation_plan_packet_date_idx"
      );
      table.index(["date_created"], "irrigation_plan_state_date_created_idx");

      // Composite primary key constraint for (irrigation_plan, packet_date)
      table.primary(["irrigation_plan", "packet_date"]);
    });

    // Ensure TimescaleDB extension is enabled
    await tx.raw(`CREATE EXTENSION IF NOT EXISTS timescaledb;`);

    // Convert table to TimescaleDB hypertable with monthly chunking
    await tx.raw(`
      SELECT create_hypertable(
        'irrigation_plan_state',
        'packet_date',
        chunk_time_interval => INTERVAL '1 month',
        if_not_exists => TRUE
      );
    `);

    // Add compression policy for data older than 3 months
    await tx.raw(`
      ALTER TABLE irrigation_plan_state SET (
        timescaledb.compress,
        timescaledb.compress_segmentby = 'irrigation_plan'
      );
    `);

    await tx.raw(`
      SELECT add_compression_policy(
        'irrigation_plan_state',
        INTERVAL '3 months'
      );
    `);

    // Add retention policy to automatically drop data older than 2 years
    await tx.raw(`
      SELECT add_retention_policy(
        'irrigation_plan_state',
        INTERVAL '2 years'
      );
    `);

    // Add table comment
    await tx.raw(`
      COMMENT ON TABLE irrigation_plan_state IS 'Historical state of irrigation plan executions based on SchedulingReportPackage MQTT messages - TimescaleDB hypertable';
    `);

    // Add column comments
    await tx.raw(`
      COMMENT ON COLUMN irrigation_plan_state.id IS 'Primary key - auto-incrementing';
      COMMENT ON COLUMN irrigation_plan_state.irrigation_plan IS 'Foreign key to irrigation_plan table';
      COMMENT ON COLUMN irrigation_plan_state.packet_date IS 'When the original device status packet was recorded - partitioning column';
      COMMENT ON COLUMN irrigation_plan_state.start_time IS 'When the irrigation plan execution started';
      COMMENT ON COLUMN irrigation_plan_state.end_time IS 'When the irrigation plan execution ended (null if still running)';
      COMMENT ON COLUMN irrigation_plan_state.activated_steps IS 'JSONB array with IDs of irrigation_plan_step records for activated sectors';
      COMMENT ON COLUMN irrigation_plan_state.activated_ferti_steps IS 'JSONB array with IDs of irrigation_plan_step records for sectors with fertigation';
      COMMENT ON COLUMN irrigation_plan_state.waterpump_working IS 'Status of the water pump during the schedule';
      COMMENT ON COLUMN irrigation_plan_state.backwash_start_time IS 'When the backwash started (nullable)';
      COMMENT ON COLUMN irrigation_plan_state.uses_waterpump IS 'Whether the water pump should be used in the schedule';
      COMMENT ON COLUMN irrigation_plan_state.uses_ferti IS 'Whether fertigation should be used in the schedule';
      COMMENT ON COLUMN irrigation_plan_state.date_created IS 'When this state record was created';
    `);
  });
}

/**
 * Migration to drop irrigation_plan_state hypertable
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    // Remove compression and retention policies
    await tx.raw(`
      SELECT remove_compression_policy('irrigation_plan_state', if_exists => true);
    `);

    await tx.raw(`
      SELECT remove_retention_policy('irrigation_plan_state', if_exists => true);
    `);

    // Drop hypertable (this automatically removes chunks)
    await tx.schema.dropTableIfExists("irrigation_plan_state");
  });
}